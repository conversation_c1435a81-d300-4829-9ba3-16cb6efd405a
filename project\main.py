#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电赛控制类万能代码模板 - K230 CanMV
基础框架，可根据具体题目需求进行扩展
版本：v1.0
"""

import time
import os
import gc
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
import image

# ==================== 基础配置类 ====================
class Config:
    """基础配置 - 根据实际需求修改"""
    # 图像配置
    SENSOR_WIDTH = 1920
    SENSOR_HEIGHT = 1080
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
    ROI = (560, 300, 800, 480)  # 调整ROI以适应800x480显示

    # 默认阈值 - 需要根据现场环境调整
    DEFAULT_THRESHOLDS = {
        'red': [(30, 100, 15, 127, 15, 127)],
        'green': [(30, 100, -64, -8, -32, 32)],
        'blue': [(0, 30, 0, 64, -128, -10)],
        'white': [(200, 255)],
        'black': [(0, 50)]
    }

# ==================== 硬件控制类 ====================
class HardwareController:
    """硬件控制器 - 根据实际硬件配置修改"""

    def __init__(self):
        self.fpioa = FPIOA()
        self.uart = None
        self.pins = {}
        self.touch = None

    def init_uart(self, tx_pin=11, rx_pin=12, baudrate=115200):
        """初始化UART通信"""
        self.fpioa.set_function(tx_pin, FPIOA.UART2_TXD)
        self.fpioa.set_function(rx_pin, FPIOA.UART2_RXD)
        self.uart = UART(UART.UART2, baudrate)
        print(f"UART初始化完成: TX={tx_pin}, RX={rx_pin}, 波特率={baudrate}")

    def init_pin(self, pin_num, mode=Pin.OUT, pull=Pin.PULL_NONE, name=None):
        """初始化GPIO引脚"""
        self.fpioa.set_function(pin_num, getattr(FPIOA, f'GPIO{pin_num}'))
        pin = Pin(pin_num, mode, pull)
        pin_name = name or f'pin_{pin_num}'
        self.pins[pin_name] = pin
        print(f"引脚初始化: {pin_name} -> Pin{pin_num}")
        return pin

    def init_touch(self):
        """初始化触摸屏"""
        self.touch = TOUCH(0)
        print("触摸屏初始化完成")

    def send_uart_data(self, data):
        """发送UART数据"""
        if self.uart:
            if isinstance(data, str):
                self.uart.write(data.encode())
            else:
                self.uart.write(bytes(data))

    def read_uart_data(self):
        """读取UART数据"""
        if self.uart and self.uart.any():
            return self.uart.read()
        return None

    def control_pin(self, pin_name, value):
        """控制引脚输出"""
        if pin_name in self.pins:
            self.pins[pin_name].value(value)

    def read_pin(self, pin_name):
        """读取引脚状态"""
        if pin_name in self.pins:
            return self.pins[pin_name].value()
        return None

    def read_touch(self):
        """读取触摸屏"""
        if self.touch:
            return self.touch.read()
        return []

# ==================== PID控制器 ====================
class PIDController:
    """PID控制器 - 通用控制算法"""

    def __init__(self, kp=0.1, ki=0.0, kd=0.0, target=0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.target = target
        self.error = 0
        self.last_error = 0
        self.integral = 0

    def calculate(self, current_value):
        """计算PID输出"""
        self.error = self.target - current_value
        self.integral += self.error
        derivative = self.error - self.last_error

        output = (self.kp * self.error +
                 self.ki * self.integral +
                 self.kd * derivative)

        self.last_error = self.error
        return output

    def reset(self):
        """重置PID参数"""
        self.error = 0
        self.last_error = 0
        self.integral = 0

# ==================== 图像处理类 ====================
class ImageProcessor:
    """图像处理器 - 基础图像识别功能"""

    def __init__(self):
        self.sensor = None
        self.thresholds = Config.DEFAULT_THRESHOLDS.copy()
        self.init_camera()

    def init_camera(self):
        """初始化摄像头"""
        try:
            self.sensor = Sensor(width=Config.SENSOR_WIDTH, height=Config.SENSOR_HEIGHT)
            self.sensor.reset()
            self.sensor.set_framesize(width=Config.SENSOR_WIDTH, height=Config.SENSOR_HEIGHT)
            self.sensor.set_pixformat(Sensor.RGB565)

            Display.init(Display.ST7701, to_ide=True,
                        width=Config.DISPLAY_WIDTH, height=Config.DISPLAY_HEIGHT)
            MediaManager.init()
            self.sensor.run()
            print("摄像头初始化完成")
        except Exception as e:
            print(f"摄像头初始化失败: {e}")

    def capture_image(self, roi=None):
        """捕获图像"""
        if not self.sensor:
            return None
        img = self.sensor.snapshot(chn=CAM_CHN_ID_0)
        if roi:
            img = img.copy(roi=roi)
        else:
            img = img.copy(roi=Config.ROI)
        return img

    def find_color_blobs(self, img, color_name='red', min_area=100):
        """查找指定颜色的色块"""
        if color_name not in self.thresholds:
            print(f"未找到颜色阈值: {color_name}")
            return []

        blobs = img.find_blobs(self.thresholds[color_name],
                              pixels_threshold=min_area,
                              area_threshold=min_area)

        # 绘制检测结果
        for blob in blobs:
            img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                             color=(0, 255, 0), thickness=2, fill=False)
            img.draw_cross(blob.cx(), blob.cy(),
                          color=(255, 0, 0), size=10, thickness=2)

        return blobs

    def find_shapes(self, img, shape_type='rect'):
        """查找几何形状"""
        if shape_type == 'rect':
            return self._find_rectangles(img)
        elif shape_type == 'circle':
            return self._find_circles(img)
        elif shape_type == 'line':
            return self._find_lines(img)
        else:
            print(f"不支持的形状类型: {shape_type}")
            return []

    def _find_rectangles(self, img):
        """查找矩形"""
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary([(0, 100)])  # 简单二值化
        rects = img_binary.find_rects(threshold=1000)

        for rect in rects:
            corners = rect.corners()
            for i in range(4):
                next_i = (i + 1) % 4
                img.draw_line(corners[i][0], corners[i][1],
                            corners[next_i][0], corners[next_i][1],
                            color=(0, 255, 0), thickness=2)
        return rects

    def _find_circles(self, img):
        """查找圆形"""
        circles = img.find_circles(threshold=2000)
        for circle in circles:
            img.draw_circle(circle.x(), circle.y(), circle.r(),
                          color=(255, 0, 0), thickness=2)
        return circles

    def _find_lines(self, img):
        """查找直线"""
        lines = img.find_lines(threshold=1000)
        for line in lines:
            img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(),
                         color=(0, 0, 255), thickness=2)
        return lines

    def show_image(self, img):
        """显示图像 - 铺满800x480屏幕"""
        if not img:
            return

        # 如果图像尺寸不是800x480，进行调整
        if img.width() != 800 or img.height() != 480:
            # 简单缩放处理
            if img.width() > 800 or img.height() > 480:
                scale = max(img.width() // 800, img.height() // 480) + 1
                img.midpoint_pool(scale, scale)

        img.compress_for_ide()
        # 铺满屏幕显示
        Display.show_image(img, x=0, y=0)

    def update_threshold(self, color_name, new_threshold):
        """更新颜色阈值"""
        self.thresholds[color_name] = new_threshold
        print(f"更新阈值 {color_name}: {new_threshold}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.sensor:
                self.sensor.stop()
            Display.deinit()
            MediaManager.deinit()
            print("图像处理器清理完成")
        except Exception as e:
            print(f"清理失败: {e}")

# ==================== 主函数 ====================
def main():
    """主函数 - 根据具体题目需求修改"""
    print("电赛控制系统启动")

    # 初始化硬件
    hardware = HardwareController()

    # 根据需要初始化硬件组件
    # hardware.init_uart(tx_pin=11, rx_pin=12, baudrate=115200)
    # hardware.init_pin(33, Pin.OUT, name='laser')  # 激光笔
    # hardware.init_pin(53, Pin.IN, Pin.PULL_DOWN, name='key')  # 按键
    hardware.init_touch()  # 启用触摸屏

    # 初始化图像处理
    image_processor = ImageProcessor()

    # 初始化PID控制器
    pid_x = PIDController(kp=-0.2, ki=-0.1, target=180)  # X轴控制
    pid_y = PIDController(kp=-0.2, ki=-0.1, target=180)  # Y轴控制

    # 主循环
    clock = time.clock()

    try:
        while True:
            clock.tick()
            os.exitpoint()

            # 捕获图像
            img = image_processor.capture_image()
            if img is None:
                continue

            # ========== 在这里添加具体的题目逻辑 ==========

            # 示例1: 查找红色目标
            red_blobs = image_processor.find_color_blobs(img, 'red', min_area=100)
            if red_blobs:
                target = red_blobs[0]  # 选择第一个目标
                target_x, target_y = target.cx(), target.cy()

                # PID控制示例
                pid_x.target = 180  # 屏幕中心X
                pid_y.target = 180  # 屏幕中心Y

                control_x = pid_x.calculate(target_x)
                control_y = pid_y.calculate(target_y)

                # 显示控制信息
                img.draw_string_advanced(10, 10, 24, f"目标: ({target_x:.1f}, {target_y:.1f})",
                                       color=(255, 255, 255))
                img.draw_string_advanced(10, 40, 24, f"控制: ({control_x:.1f}, {control_y:.1f})",
                                       color=(255, 255, 0))

                # 发送控制命令（根据实际硬件修改）
                # hardware.send_uart_data([0xAA, int(control_x), int(control_y), 0xFF])

            # 示例2: 查找几何形状
            # rects = image_processor.find_shapes(img, 'rect')
            # circles = image_processor.find_shapes(img, 'circle')

            # 示例3: 读取按键和触摸
            touch_points = hardware.read_touch()
            if touch_points:
                print(f"检测到触摸: {touch_points[0].x}, {touch_points[0].y}")
                print("进入阈值调整模式...")
                threshold_adjust_mode(image_processor, hardware.touch)
                print("退出阈值调整模式，继续主程序...")
                # 清理触摸状态，避免重复触发
                time.sleep(0.5)
                hardware.read_touch()  # 清空触摸缓冲
                continue  # 跳过本次循环，重新开始

            # 显示图像
            image_processor.show_image(img)

            # 显示帧率信息
            img.draw_string_advanced(10, 450, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

            # 内存管理
            gc.collect()

    except KeyboardInterrupt:
        print("用户停止程序")
    except Exception as e:
        print(f"程序异常: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源
        image_processor.cleanup()
        print("程序结束")

# ==================== 工具函数 ====================
def send_motor_command(uart, motor_id, direction, steps):
    """发送电机控制命令到下位机"""
    if uart is None:
        return

    # 标准协议格式: [0xAA, 0xAA, motor_id, direction, steps_high, steps_low, checksum, 0xFF, 0xFF]
    cmd = [0xAA, 0xAA, motor_id, direction,
           (steps >> 8) & 0xFF, steps & 0xFF, 0x00, 0xFF, 0xFF]
    cmd[6] = sum(cmd[2:6]) & 0xFF  # 校验和
    uart.write(bytes(cmd))

def threshold_adjust_mode(image_processor, touch):
    """阈值调整模式 - 适配800x400屏幕"""
    print("进入阈值调整模式")

    # 当前调整的颜色和参数
    current_color = 'red'
    color_list = ['red', 'green', 'blue', 'white', 'black']
    color_index = 0

    # LAB阈值参数 [L_min, L_max, A_min, A_max, B_min, B_max]
    current_threshold = list(image_processor.thresholds[current_color][0])
    param_index = 0  # 当前调整的参数索引
    param_names = ['L_min', 'L_max', 'A_min', 'A_max', 'B_min', 'B_max']

    last_touch_time = time.ticks_ms()

    while True:
        img = image_processor.capture_image()
        if img is None:
            continue

        # 缩放图像适应屏幕
        if img.width() > 400 or img.height() > 300:
            scale = max(img.width() // 400, img.height() // 300) + 1
            img.midpoint_pool(scale, scale)

        # 应用当前阈值测试效果
        test_threshold = [tuple(current_threshold)]
        if current_color in ['white', 'black']:
            # 灰度阈值
            test_threshold = [tuple(current_threshold[:2])]

        blobs = img.find_blobs(test_threshold, pixels_threshold=50)

        # 绘制检测结果
        for blob in blobs:
            img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                             color=(0, 255, 0), thickness=2, fill=False)

        # 创建UI界面
        ui_img = image.Image(800, 480, image.RGB565)
        ui_img.draw_rectangle(0, 0, 800, 480, color=(0, 0, 0), fill=True)

        # 显示处理后的图像（左侧）
        img_x = 10
        img_y = 10
        ui_img.draw_image(img, img_x, img_y)

        # 右侧显示控制界面
        info_x = img_x + img.width() + 20
        info_y = 10

        # 标题
        ui_img.draw_string_advanced(info_x, info_y, 24, "阈值调整", color=(255, 255, 255))
        info_y += 35

        # 当前颜色
        ui_img.draw_string_advanced(info_x, info_y, 20, f"颜色: {current_color}", color=(255, 255, 0))
        info_y += 30

        # 显示当前阈值参数
        for i, (name, value) in enumerate(zip(param_names, current_threshold)):
            if current_color in ['white', 'black'] and i >= 2:
                break  # 灰度只显示前两个参数

            color = (255, 0, 0) if i == param_index else (200, 200, 200)
            ui_img.draw_string_advanced(info_x, info_y, 18, f"{name}: {value}", color=color)
            info_y += 25

        # 操作说明
        info_y += 20
        ui_img.draw_string_advanced(info_x, info_y, 16, "操作说明:", color=(0, 255, 255))
        info_y += 25
        ui_img.draw_string_advanced(info_x, info_y, 14, "左上角: 切换颜色", color=(255, 255, 255))
        info_y += 20
        ui_img.draw_string_advanced(info_x, info_y, 14, "右上角: 切换参数", color=(255, 255, 255))
        info_y += 20
        ui_img.draw_string_advanced(info_x, info_y, 14, "左下角: 减少数值", color=(255, 255, 255))
        info_y += 20
        ui_img.draw_string_advanced(info_x, info_y, 14, "右下角: 增加数值", color=(255, 255, 255))
        info_y += 20
        ui_img.draw_string_advanced(info_x, info_y, 14, "中间: 保存退出", color=(255, 255, 255))

        # 绘制触摸区域提示
        # 左上角 - 切换颜色
        ui_img.draw_rectangle(0, 0, 150, 80, color=(100, 100, 100), thickness=2, fill=False)
        ui_img.draw_string_advanced(10, 35, 16, "切换颜色", color=(255, 255, 255))

        # 右上角 - 切换参数
        ui_img.draw_rectangle(650, 0, 150, 80, color=(100, 100, 100), thickness=2, fill=False)
        ui_img.draw_string_advanced(660, 35, 16, "切换参数", color=(255, 255, 255))

        # 左下角 - 减少
        ui_img.draw_rectangle(0, 400, 150, 80, color=(100, 100, 100), thickness=2, fill=False)
        ui_img.draw_string_advanced(10, 435, 16, "减少(-5)", color=(255, 255, 255))

        # 右下角 - 增加
        ui_img.draw_rectangle(650, 400, 150, 80, color=(100, 100, 100), thickness=2, fill=False)
        ui_img.draw_string_advanced(660, 435, 16, "增加(+5)", color=(255, 255, 255))

        # 中间 - 保存退出
        ui_img.draw_rectangle(300, 200, 200, 100, color=(0, 100, 0), thickness=2, fill=False)
        ui_img.draw_string_advanced(350, 245, 18, "保存退出", color=(0, 255, 0))

        # 显示界面
        image_processor.show_image(ui_img)

        # 处理触摸输入
        touch_points = touch.read() if touch else []
        current_time = time.ticks_ms()

        if touch_points:
            x, y = touch_points[0].x, touch_points[0].y

            # 防抖处理
            if time.ticks_diff(current_time, last_touch_time) > 300:
                if x < 150 and y < 80:  # 左上角 - 切换颜色
                    color_index = (color_index + 1) % len(color_list)
                    current_color = color_list[color_index]
                    current_threshold = list(image_processor.thresholds[current_color][0])
                    param_index = 0
                    print(f"切换到颜色: {current_color}")

                elif x > 650 and y < 80:  # 右上角 - 切换参数
                    max_params = 2 if current_color in ['white', 'black'] else 6
                    param_index = (param_index + 1) % max_params
                    print(f"切换到参数: {param_names[param_index]}")

                elif x < 150 and y > 400:  # 左下角 - 减少
                    current_threshold[param_index] = max(-128, current_threshold[param_index] - 5)
                    print(f"{param_names[param_index]} = {current_threshold[param_index]}")

                elif x > 650 and y > 400:  # 右下角 - 增加
                    current_threshold[param_index] = min(255, current_threshold[param_index] + 5)
                    print(f"{param_names[param_index]} = {current_threshold[param_index]}")

                elif 300 < x < 500 and 200 < y < 300:  # 中间 - 保存退出
                    # 保存当前阈值
                    image_processor.thresholds[current_color] = [tuple(current_threshold)]
                    print(f"保存阈值 {current_color}: {current_threshold}")
                    print("阈值已保存，准备退出...")
                    # 清理触摸状态
                    time.sleep(0.3)
                    break

                last_touch_time = current_time

        time.sleep(0.05)

    print("退出阈值调整模式")
    # 额外的清理工作
    time.sleep(0.2)
    if touch:
        touch.read()  # 清空触摸缓冲



# ==================== 程序入口 ====================
if __name__ == "__main__":
    main()
