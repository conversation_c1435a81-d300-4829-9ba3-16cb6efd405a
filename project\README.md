# 电赛控制类万能代码模板 - K230 CanMV

## 📋 项目简介

这是一个专为全国大学生电子设计竞赛控制类题目设计的**基础代码模板**，基于K230 CanMV开发板。提供了图像识别、PID控制、UART通信等基础功能框架，可根据具体题目需求快速扩展。

## 🚀 主要特性

- **模块化设计**: 硬件控制、图像处理、PID控制独立模块
- **基础图像识别**: 颜色识别、几何形状检测
- **PID控制算法**: 通用PID控制器
- **UART通信**: 标准串口通信协议
- **触摸屏支持**: 基础触摸交互
- **配置化参数**: 引脚、阈值等参数可配置
- **简洁易扩展**: 代码结构清晰，便于添加新功能

## 📁 文件结构

```
K230/
├── main_template.py           # 主程序模板
├── contest_config_template.py # 基础配置文件
├── contest_utils_template.py  # 工具函数模块
├── threshold_test.py          # 阈值调整测试程序
├── README.md                  # 说明文档
├── 15_2023年E题基础部分.py     # 2023年E题参考代码
└── Touch.py                   # 触摸屏测试程序
```

## 🔧 基础配置

### 引脚配置（在contest_config.py中修改）
```python
PINS = {
    'uart_tx': 11,      # UART发送
    'uart_rx': 12,      # UART接收
    'laser': 33,        # 激光笔控制
    'led': 52,          # LED指示灯
    'key1': 53,         # 按键1
    # 根据实际需要添加更多引脚
}
```

### 颜色阈值配置
```python
COLOR_THRESHOLDS = {
    'red': [(30, 100, 15, 127, 15, 127)],
    'green': [(30, 100, -64, -8, -32, 32)],
    'blue': [(0, 30, 0, 64, -128, -10)],
    # 需要根据现场环境调整
}
```

### 🎯 脱机阈值调整功能

代码内置了适配800x480屏幕的阈值调整界面，支持脱机调整：

**界面布局：**
- 左侧：实时图像显示（应用当前阈值效果）
- 右侧：参数调整界面

**触摸操作：**
- 左上角：切换颜色（红、绿、蓝、白、黑）
- 右上角：切换参数（L_min, L_max, A_min, A_max, B_min, B_max）
- 左下角：减少当前参数值（-5）
- 右下角：增加当前参数值（+5）
- 中间绿色区域：保存并退出

## 🛠️ 快速开始

### 1. 基础使用
```python
# 直接运行主程序模板
python main_template.py

# 测试阈值调整功能
python threshold_test.py
```

### 2. 根据题目需求修改
```python
# 在main_template.py的主函数中添加具体逻辑
def main():
    # 初始化硬件
    hardware = HardwareController()
    hardware.init_uart(tx_pin=11, rx_pin=12)
    hardware.init_pin(33, Pin.OUT, name='laser')

    # 初始化图像处理
    image_processor = ImageProcessor()

    # 主循环中添加题目逻辑
    while True:
        img = image_processor.capture_image()

        # 示例：查找红色目标
        red_blobs = image_processor.find_color_blobs(img, 'red')
        if red_blobs:
            target = red_blobs[0]
            # 添加控制逻辑

        image_processor.show_image(img)
```

### 3. 配置参数
```python
# 修改contest_config_template.py中的参数
from contest_config_template import update_threshold

# 更新颜色阈值
update_threshold('red', [(40, 100, 20, 127, 20, 127)])
```

## 📊 使用建议

### 图像处理优化
- 根据现场环境调整ROI区域
- 使用阈值调整模式优化颜色识别
- 合理设置最小面积过滤噪点

### 控制算法调整
- 根据系统响应调整PID参数
- 使用死区控制减少震荡
- 考虑系统延迟进行补偿

## 🔍 常见问题

### 1. 颜色识别不准确
```python
# 解决方案1：代码中调整阈值
from contest_config import update_threshold
update_threshold('red', [(新的阈值)])

# 解决方案2：使用脱机阈值调整界面
# 在主程序中触摸屏幕即可进入调整模式
threshold_adjust_mode(image_processor, hardware.touch)
```

### 2. UART通信问题
```python
# 检查连接和配置
hardware.init_uart(tx_pin=11, rx_pin=12, baudrate=115200)

# 发送测试数据
hardware.send_uart_data("test")
```

### 3. 系统性能问题
```python
# 使用计时器监控性能
timer = SimpleTimer()
# ... 处理代码 ...
print(f"处理时间: {timer.tick()}ms")

# 定期清理内存
import gc
gc.collect()
```

## 📈 扩展示例

### 1. 添加新的硬件控制
```python
# 添加舵机控制
hardware.init_pin(35, Pin.OUT, name='servo1')

# 控制舵机
def control_servo(angle):
    # 根据角度计算PWM占空比
    duty = map_value(angle, 0, 180, 5, 25)
    # 发送PWM信号或UART命令
    hardware.send_uart_data([0xAA, 0x01, int(duty), 0xFF])
```

### 2. 实现路径追踪
```python
from contest_utils import generate_rectangle_path

# 生成矩形路径
path = generate_rectangle_path(center=(180, 180), width=100, height=80)

# 追踪路径
for point in path:
    pid_x.target = point[0]
    pid_y.target = point[1]
    # 执行控制逻辑
```

### 3. 多目标处理
```python
# 查找多个目标
red_blobs = image_processor.find_color_blobs(img, 'red')
blue_blobs = image_processor.find_color_blobs(img, 'blue')

# 选择最大的目标
if red_blobs:
    target = find_largest_blob(red_blobs)
    # 处理目标
```

## 🏆 适用场景

### 典型控制类题目
- **激光笔控制系统**: 目标追踪、轨迹描绘
- **机械臂控制**: 视觉定位、精确抓取
- **移动机器人**: 路径规划、障碍避免
- **自动分拣系统**: 目标识别、分类控制

## 📝 开发流程建议

### 1. 题目分析阶段
- 仔细阅读题目要求
- 确定需要的硬件模块
- 分析图像识别需求

### 2. 硬件连接阶段
- 根据题目修改PINS配置
- 测试各硬件模块功能
- 确认通信协议

### 3. 算法开发阶段
- 调整颜色阈值
- 实现控制算法
- 测试系统性能

### 4. 优化调试阶段
- 优化识别精度
- 调整控制参数
- 提高系统稳定性

## 📞 使用说明

这是一个**基础代码模板**，需要根据具体题目进行修改：

1. **修改硬件配置**: 在`contest_config.py`中配置引脚
2. **调整图像参数**: 根据现场环境调整阈值和ROI
3. **实现控制逻辑**: 在`main.py`中添加具体的控制算法
4. **测试和优化**: 使用`test_modules.py`测试各模块功能

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。

---

**这只是一个基础模板，具体实现需要根据2025年电赛题目进行开发！** 🚀
