#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8路灰度传感器循迹程序 - 基于官方例程修正版
解决LCD显示问题
版本：v2.0
"""

import time
import os
import gc
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
import image

# ==================== 显示配置（基于官方例程）====================
DISPLAY_MODE = "LCD"  # LCD模式

if DISPLAY_MODE == "LCD":
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
else:
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080

# ==================== 8路灰度传感器循迹类 ====================
class LineFollower:
    """8路灰度传感器循迹器"""
    
    def __init__(self, sensor_count=8, sensor_width=60, sensor_height=30):
        self.sensor_count = sensor_count
        self.sensor_width = sensor_width
        self.sensor_height = sensor_height
        self.sensor_positions = []
        self.sensor_values = [0] * sensor_count
        self.line_position = 0
        self.last_line_position = 0
        self.line_detected = False
        
        # PID控制参数
        self.kp = 1.5
        self.ki = 0.2
        self.kd = 1.0
        self.error = 0
        self.last_error = 0
        self.integral = 0
        
        # 阈值设置
        self.line_threshold = 60  # 根据你的测试调整
        self.init_sensor_positions()
    
    def init_sensor_positions(self):
        """初始化传感器位置"""
        start_x = 50
        end_x = 750
        spacing = (end_x - start_x) / (self.sensor_count - 1)
        
        for i in range(self.sensor_count):
            x = int(start_x + i * spacing)
            y = 400
            self.sensor_positions.append((x, y))
    
    def read_sensors(self, img):
        """读取传感器数值"""
        if not img:
            return self.sensor_values
        
        gray_img = img.to_grayscale(copy=True)
        
        for i, (x, y) in enumerate(self.sensor_positions):
            roi_x = max(0, x - self.sensor_width // 2)
            roi_y = max(0, y - self.sensor_height // 2)
            roi_w = min(self.sensor_width, img.width() - roi_x)
            roi_h = min(self.sensor_height, img.height() - roi_y)
            
            if roi_w > 0 and roi_h > 0:
                roi_img = gray_img.copy(roi=(roi_x, roi_y, roi_w, roi_h))
                stats = roi_img.get_statistics()
                self.sensor_values[i] = stats.mean()
            else:
                self.sensor_values[i] = 128
        
        return self.sensor_values
    
    def detect_line_position(self):
        """检测线条位置"""
        binary_sensors = [1 if value < self.line_threshold else 0 for value in self.sensor_values]
        self.line_detected = any(binary_sensors)
        
        if not self.line_detected:
            self.line_position = self.last_line_position
            return self.line_position
        
        weighted_sum = 0
        total_weight = 0
        
        for i, sensor_active in enumerate(binary_sensors):
            if sensor_active:
                weighted_sum += i * 1000
                total_weight += 1
        
        if total_weight > 0:
            self.line_position = weighted_sum // total_weight
            self.last_line_position = self.line_position
        else:
            self.line_position = self.last_line_position
        
        return self.line_position
    
    def calculate_pid_output(self):
        """计算PID控制输出"""
        target_position = 3500
        self.error = target_position - self.line_position
        
        self.integral += self.error
        derivative = self.error - self.last_error
        
        output = (self.kp * self.error + 
                 self.ki * self.integral + 
                 self.kd * derivative)
        
        self.last_error = self.error
        return max(-100, min(100, output))
    
    def get_motor_speeds(self, base_speed=50):
        """计算左右电机速度"""
        pid_output = self.calculate_pid_output()
        left_speed = base_speed - pid_output
        right_speed = base_speed + pid_output
        
        left_speed = max(-100, min(100, left_speed))
        right_speed = max(-100, min(100, right_speed))
        
        return left_speed, right_speed
    
    def draw_sensors(self, img):
        """绘制传感器状态"""
        if not img:
            return
        
        for i, (x, y) in enumerate(self.sensor_positions):
            if self.sensor_values[i] < self.line_threshold:
                color = (255, 0, 0)  # 红色 - 检测到线条
            else:
                color = (0, 255, 0)  # 绿色 - 背景
            
            img.draw_rectangle(x - self.sensor_width // 2, 
                             y - self.sensor_height // 2,
                             self.sensor_width, 
                             self.sensor_height,
                             color=color, thickness=2, fill=False)
            
            # 显示传感器编号和数值
            img.draw_string_advanced(x - 10, y - 35, 16, f"S{i}", color=(255, 255, 255))
            img.draw_string_advanced(x - 15, y + 25, 14, f"{self.sensor_values[i]:.0f}", color=(255, 255, 255))
        
        # 绘制线条位置指示
        if self.line_detected:
            line_x = int(50 + (self.line_position / 1000) * (700 / 7))
            img.draw_line(line_x, 350, line_x, 450, color=(255, 255, 0), thickness=3)
            img.draw_string_advanced(line_x - 20, 320, 16, "LINE", color=(255, 255, 0))
    
    def draw_info(self, img):
        """绘制循迹信息"""
        if not img:
            return
        
        # 显示传感器数值
        sensor_str = "传感器: " + " ".join([f"{v:.0f}" for v in self.sensor_values])
        img.draw_string_advanced(10, 10, 16, sensor_str, color=(255, 255, 255))
        
        # 显示二进制状态
        binary_sensors = [1 if v < self.line_threshold else 0 for v in self.sensor_values]
        binary_str = "检测状态: " + " ".join([f"{b}" for b in binary_sensors])
        img.draw_string_advanced(10, 30, 16, binary_str, color=(255, 255, 0))
        
        # 显示阈值
        img.draw_string_advanced(10, 50, 16, f"检测阈值: {self.line_threshold}", color=(200, 200, 200))
        
        # 显示线条位置
        img.draw_string_advanced(10, 70, 16, f"线条位置: {self.line_position:.0f}/7000", color=(255, 255, 0))
        
        # 显示PID信息
        pid_output = self.calculate_pid_output()
        img.draw_string_advanced(10, 90, 16, f"PID输出: {pid_output:.1f}", color=(0, 255, 255))
        
        # 显示电机速度
        left_speed, right_speed = self.get_motor_speeds()
        img.draw_string_advanced(10, 110, 16, f"电机速度 L:{left_speed:.0f} R:{right_speed:.0f}", color=(255, 0, 255))
        
        # 显示检测状态
        status = "检测到线条" if self.line_detected else "未检测到线条"
        color = (0, 255, 0) if self.line_detected else (255, 0, 0)
        img.draw_string_advanced(10, 130, 16, f"状态: {status}", color=color)
        
        # 显示操作提示
        img.draw_string_advanced(10, 160, 14, "触摸屏幕 -> 阈值调整", color=(255, 200, 0))
        
        # 显示LCD状态
        img.draw_string_advanced(10, 180, 14, "LCD显示模式 - 修正版", color=(0, 255, 255))
    
    def set_pid_params(self, kp, ki, kd):
        """设置PID参数"""
        self.kp = kp
        self.ki = ki
        self.kd = kd
    
    def set_threshold(self, threshold):
        """设置线条检测阈值"""
        self.line_threshold = threshold

# ==================== 图像处理类（基于官方例程）====================
class ImageProcessor:
    """基于官方例程的图像处理器"""
    
    def __init__(self):
        self.sensor = None
        self.init_camera()
    
    def init_camera(self):
        """初始化摄像头 - 使用官方例程的正确方式"""
        try:
            print("正在初始化摄像头...")
            
            # 初始化传感器
            self.sensor = Sensor(width=1920, height=1080)
            self.sensor.reset()
            self.sensor.set_framesize(width=1920, height=1080)
            self.sensor.set_pixformat(Sensor.RGB565)
            
            # 使用官方例程的正确显示初始化方式
            print("配置LCD显示...")
            if DISPLAY_MODE == "LCD":
                # 关键修正：to_ide=True 才是正确的LCD显示方式！
                Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
            else:
                Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
            
            MediaManager.init()
            self.sensor.run()
            
            print("✓ 摄像头初始化完成 - LCD屏幕显示")
            print("✓ 使用官方例程的正确显示方式")
            
        except Exception as e:
            print(f"✗ 摄像头初始化失败: {e}")
    
    def capture_image(self):
        """捕获图像"""
        if not self.sensor:
            return None
        img = self.sensor.snapshot(chn=CAM_CHN_ID_0)
        # 裁剪到合适的区域
        img = img.copy(roi=(560, 300, 800, 480))
        return img
    
    def show_image(self, img):
        """显示图像 - 使用官方例程方式"""
        if not img:
            return
        
        # 确保图像尺寸适合显示
        if img.width() != DISPLAY_WIDTH or img.height() != DISPLAY_HEIGHT:
            if img.width() > DISPLAY_WIDTH or img.height() > DISPLAY_HEIGHT:
                scale = max(img.width() // DISPLAY_WIDTH, img.height() // DISPLAY_HEIGHT) + 1
                img.midpoint_pool(scale, scale)
        
        # 使用官方例程的显示方式
        Display.show_image(img)
    
    def cleanup(self):
        """清理资源 - 基于官方例程"""
        try:
            if self.sensor:
                self.sensor.stop()
            Display.deinit()
            time.sleep_ms(100)
            MediaManager.deinit()
            print("图像处理器清理完成")
        except Exception as e:
            print(f"清理失败: {e}")

# ==================== 硬件控制类 ====================
class HardwareController:
    """硬件控制器"""
    
    def __init__(self):
        self.fpioa = FPIOA()
        self.uart = None
        self.touch = None
    
    def init_uart(self, tx_pin=11, rx_pin=12, baudrate=115200):
        """初始化UART"""
        try:
            self.fpioa.set_function(tx_pin, FPIOA.UART2_TXD)
            self.fpioa.set_function(rx_pin, FPIOA.UART2_RXD)
            self.uart = UART(UART.UART2, baudrate)
            print(f"✓ UART初始化完成")
        except Exception as e:
            print(f"✗ UART初始化失败: {e}")
    
    def init_touch(self):
        """初始化触摸屏"""
        try:
            self.touch = TOUCH(0)
            print("✓ 触摸屏初始化完成")
        except Exception as e:
            print(f"✗ 触摸屏初始化失败: {e}")
    
    def send_uart_data(self, data):
        """发送UART数据"""
        if self.uart:
            if isinstance(data, str):
                self.uart.write(data.encode())
            else:
                self.uart.write(bytes(data))
    
    def read_touch(self):
        """读取触摸屏"""
        if self.touch:
            return self.touch.read()
        return []

# ==================== 简化的阈值调整 ====================
def simple_threshold_adjust(image_processor, touch, line_follower):
    """简化的阈值调整模式"""
    print("进入阈值调整模式")
    print("触摸操作：左侧减少阈值，右侧增加阈值，中间退出")

    last_touch_time = time.ticks_ms()

    while True:
        img = image_processor.capture_image()
        if img is None:
            continue

        # 实时更新循迹
        line_follower.read_sensors(img)
        line_follower.detect_line_position()
        line_follower.draw_sensors(img)
        line_follower.draw_info(img)

        # 添加调整说明
        img.draw_string_advanced(10, 200, 18, "=== 阈值调整模式 ===", color=(255, 255, 0))
        img.draw_string_advanced(10, 225, 16, f"当前阈值: {line_follower.line_threshold}", color=(255, 255, 255))
        img.draw_string_advanced(10, 250, 14, "触摸左侧: 阈值-5", color=(255, 255, 255))
        img.draw_string_advanced(10, 270, 14, "触摸右侧: 阈值+5", color=(255, 255, 255))
        img.draw_string_advanced(10, 290, 14, "触摸中间: 退出", color=(255, 255, 255))

        # 显示传感器详细状态
        img.draw_string_advanced(10, 320, 14, "传感器状态分析:", color=(0, 255, 255))
        for i in range(min(4, len(line_follower.sensor_values))):
            value = line_follower.sensor_values[i]
            status = "黑线" if value < line_follower.line_threshold else "背景"
            color = (255, 0, 0) if value < line_follower.line_threshold else (0, 255, 0)
            img.draw_string_advanced(10, 340 + i * 15, 12, f"S{i}: {value:.0f} ({status})", color=color)

        for i in range(4, min(8, len(line_follower.sensor_values))):
            value = line_follower.sensor_values[i]
            status = "黑线" if value < line_follower.line_threshold else "背景"
            color = (255, 0, 0) if value < line_follower.line_threshold else (0, 255, 0)
            img.draw_string_advanced(400, 340 + (i-4) * 15, 12, f"S{i}: {value:.0f} ({status})", color=color)

        # 显示图像
        image_processor.show_image(img)

        # 处理触摸输入
        touch_points = touch.read() if touch else []
        current_time = time.ticks_ms()

        if touch_points:
            x, y = touch_points[0].x, touch_points[0].y

            if time.ticks_diff(current_time, last_touch_time) > 300:
                if x < 200:  # 左侧 - 减少阈值
                    line_follower.line_threshold = max(0, line_follower.line_threshold - 5)
                    print(f"阈值减少到: {line_follower.line_threshold}")

                elif x > 600:  # 右侧 - 增加阈值
                    line_follower.line_threshold = min(255, line_follower.line_threshold + 5)
                    print(f"阈值增加到: {line_follower.line_threshold}")

                elif 200 <= x <= 600:  # 中间 - 退出
                    print(f"阈值调整完成: {line_follower.line_threshold}")
                    time.sleep(0.3)
                    break

                last_touch_time = current_time

        time.sleep(0.05)
        os.exitpoint()  # 添加退出点

    print("退出阈值调整模式")
    time.sleep(0.2)
    if touch:
        touch.read()

# ==================== 电机控制函数 ====================
def send_motor_command(uart, motor_id, direction, steps):
    """发送电机控制命令"""
    if uart is None:
        return

    # 简化的控制命令格式
    if motor_id == 1:  # 左电机
        cmd = f"L{direction}{steps:03d}\n"
    else:  # 右电机
        cmd = f"R{direction}{steps:03d}\n"

    uart.write(cmd.encode())

# ==================== 主程序（基于官方例程）====================
def main():
    """主程序 - 基于官方例程的正确显示方式"""
    print("=" * 50)
    print("8路灰度传感器循迹系统 - 修正版")
    print("基于官方例程的正确LCD显示方式")
    print("=" * 50)
    print("功能：循迹 + 阈值调整")
    print("显示：LCD屏幕输出（to_ide=True）")
    print("操作：触摸屏幕进入阈值调整")
    print("=" * 50)

    # 启用退出点（基于官方例程）
    os.exitpoint(os.EXITPOINT_ENABLE)

    # 初始化硬件
    print("正在初始化硬件...")
    hardware = HardwareController()
    hardware.init_uart(tx_pin=11, rx_pin=12, baudrate=115200)
    hardware.init_touch()

    # 初始化图像处理
    print("正在初始化图像处理...")
    image_processor = ImageProcessor()

    # 初始化循迹器
    print("正在初始化循迹器...")
    line_follower = LineFollower(sensor_count=8, sensor_width=60, sensor_height=30)
    line_follower.set_threshold(60)  # 根据你的测试：背景80，黑线20
    line_follower.set_pid_params(kp=1.5, ki=0.2, kd=1.0)

    print("✓ 所有组件初始化完成")
    print("✓ 开始循迹程序...")

    # 主循环
    clock = time.clock()
    frame_count = 0

    try:
        while True:
            clock.tick()
            os.exitpoint()  # 退出点
            frame_count += 1

            # 捕获图像
            img = image_processor.capture_image()
            if img is None:
                continue

            # 执行循迹算法
            line_follower.read_sensors(img)
            line_follower.detect_line_position()
            left_speed, right_speed = line_follower.get_motor_speeds(base_speed=50)

            # 发送电机控制命令
            if hardware.uart and frame_count % 5 == 0:
                send_motor_command(hardware.uart, 1, 1 if left_speed >= 0 else 0, abs(int(left_speed)))
                send_motor_command(hardware.uart, 2, 1 if right_speed >= 0 else 0, abs(int(right_speed)))

            # 绘制传感器和信息
            line_follower.draw_sensors(img)
            line_follower.draw_info(img)

            # 显示帧率
            img.draw_string_advanced(650, 450, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

            # 处理触摸输入
            touch_points = hardware.read_touch()
            if touch_points:
                print("进入阈值调整模式...")
                simple_threshold_adjust(image_processor, hardware.touch, line_follower)
                print("继续循迹...")
                time.sleep(0.5)
                hardware.read_touch()
                continue

            # 显示图像到LCD屏幕（使用官方例程方式）
            image_processor.show_image(img)

            # 内存管理
            if frame_count % 30 == 0:
                gc.collect()

    except KeyboardInterrupt as e:
        print("用户停止程序:", e)
    except Exception as e:
        print(f"程序异常: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源（基于官方例程）
        image_processor.cleanup()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        print("程序结束")

if __name__ == "__main__":
    main()
