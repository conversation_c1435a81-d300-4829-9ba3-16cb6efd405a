# 8路灰度传感器循迹系统使用说明

## 功能概述

本系统实现了基于视觉的8路灰度传感器循迹功能，通过摄像头图像模拟传统的灰度传感器阵列，实现智能循迹控制。

## 主要特性

### 1. 8路传感器模拟
- 在图像底部均匀分布8个虚拟传感器
- 每个传感器检测对应区域的平均灰度值
- 可调节传感器尺寸和位置

### 2. 线条检测算法
- 基于灰度阈值的二值化检测
- 加权平均算法计算线条中心位置
- 支持线条丢失时的位置记忆

### 3. PID控制算法
- 经典PID控制器实现
- 可调节Kp、Ki、Kd参数
- 输出左右电机差速控制信号

### 4. 实时参数调试
- 触摸屏交互式参数调节
- 实时预览调节效果
- 支持阈值、PID参数调节

## 文件说明

### maintest.py
主程序文件，包含完整的循迹系统实现：
- `LineFollower` 类：核心循迹算法
- `HardwareController` 类：硬件接口控制
- `ImageProcessor` 类：图像处理功能
- `line_follow_debug_mode()` 函数：参数调试界面

### line_follow_demo.py
简化的演示程序，专门用于循迹功能测试：
- `simple_line_follow_demo()` 函数：完整循迹演示
- `test_sensor_positions()` 函数：传感器位置测试

## 使用方法

### 1. 基本运行
```python
# 运行主程序
python maintest.py

# 或运行演示程序
python line_follow_demo.py
```

### 2. 参数调节
程序运行时，触摸屏幕进入参数调试模式：

**触摸区域功能：**
- 左上角：切换调节参数
- 右上角：重置当前参数
- 左下角：减少参数值
- 右下角：增加参数值
- 中间区域：保存并退出

**可调节参数：**
- `threshold`：线条检测阈值（0-255）
- `kp`：比例控制参数（0-10）
- `ki`：积分控制参数（0-10）
- `kd`：微分控制参数（0-10）
- `base_speed`：基础速度（0-100）

### 3. 硬件连接
- UART通信：TX=Pin11, RX=Pin12, 波特率115200
- 触摸屏：用于参数调节
- 摄像头：用于图像采集

## 核心算法

### 1. 传感器读取
```python
def read_sensors(self, img):
    # 转换为灰度图像
    gray_img = img.to_grayscale(copy=True)
    
    # 在每个传感器位置采样
    for i, (x, y) in enumerate(self.sensor_positions):
        roi_img = gray_img.copy(roi=(roi_x, roi_y, roi_w, roi_h))
        stats = roi_img.get_statistics()
        self.sensor_values[i] = stats.mean()
```

### 2. 线条位置计算
```python
def detect_line_position(self):
    # 二值化处理
    binary_sensors = [1 if value < threshold else 0 for value in sensor_values]
    
    # 加权平均计算位置
    weighted_sum = sum(i * 1000 * active for i, active in enumerate(binary_sensors))
    total_weight = sum(binary_sensors)
    line_position = weighted_sum // total_weight if total_weight > 0 else last_position
```

### 3. PID控制
```python
def calculate_pid_output(self):
    error = target_position - line_position
    integral += error
    derivative = error - last_error
    
    output = kp * error + ki * integral + kd * derivative
    return output
```

## 调试技巧

### 1. 传感器位置调试
- 运行 `test_sensor_positions()` 查看传感器布局
- 调节 `sensor_width` 和 `sensor_height` 参数
- 确保传感器覆盖预期的检测区域

### 2. 阈值调节
- 在不同光照条件下测试
- 线条区域灰度值应低于阈值
- 背景区域灰度值应高于阈值

### 3. PID参数调节
- **Kp**：主要控制响应速度，过大会震荡
- **Ki**：消除稳态误差，过大会不稳定
- **Kd**：减少超调，提高稳定性

### 4. 常见问题
- **检测不到线条**：降低阈值或改善光照
- **循迹震荡**：减小Kp值或增加Kd值
- **响应迟缓**：增加Kp值或减小积分时间

## 扩展功能

### 1. 增加传感器数量
```python
line_follower = LineFollower(sensor_count=16)  # 16路传感器
```

### 2. 自定义传感器布局
```python
# 修改 init_sensor_positions() 方法
def init_sensor_positions(self):
    # 自定义传感器位置
    self.sensor_positions = [(x1, y1), (x2, y2), ...]
```

### 3. 添加速度控制
```python
def adaptive_speed_control(self, line_position):
    # 根据线条位置调节速度
    if abs(error) > threshold:
        return low_speed  # 转弯时减速
    else:
        return high_speed  # 直线时加速
```

## 性能优化

1. **图像处理优化**：使用ROI减少处理区域
2. **算法优化**：减少不必要的计算
3. **内存管理**：定期调用 `gc.collect()`
4. **帧率控制**：根据需要调节处理频率

## 注意事项

1. 确保摄像头正确初始化
2. 调节参数时要逐步进行
3. 保存重要的参数配置
4. 在不同环境下重新校准
5. 注意UART通信的数据格式

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 摄像头是否正常工作
3. 参数设置是否合理
4. 环境光照是否适宜
