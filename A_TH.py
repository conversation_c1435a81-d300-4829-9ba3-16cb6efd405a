# 阈值调整工具 - A_TH.py
# 提取自14_脱机调整阈值.py，专门用于调整矩形和激光点的阈值
# 改善了可视化UI，增加了LAB颜色空间的文本标识
#
# 使用说明：
# 1. 运行程序后显示摄像头画面，可以看到FPS和提示信息
# 2. 长按屏幕约1秒进入阈值调整模式
# 3. 在阈值调整模式中：
#    - 点击"切换"按钮在矩形检测(灰度)和激光点检测(LAB)之间切换
#    - 使用左侧按钮减少阈值，右侧按钮增加阈值
#    - LAB颜色空间说明：L(亮度) A(绿-红轴) B(蓝-黄轴)
#    - 点击"保存"保存当前阈值到阈值字典
#    - 点击"归位"重置所有阈值为默认值
#    - 点击"返回"回到正常显示模式
# 4. 保存阈值后，在正常模式下可以看到检测效果
#
# 改进功能：
# - 增加了LAB颜色空间的详细标识
# - 实时显示阈值调整效果
# - 显示检测结果（矩形框或激光点）
# - 显示覆盖率统计信息
# - 改善了按钮布局和用户体验

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import TOUCH


try:
    # 初始化传感器
    sensor = Sensor(width=1920, height=1080)
    sensor.reset()
#    sensor.set_hmirror(True)
#    sensor.set_vflip(True)
    sensor.set_framesize(width=1920, height=1080)
    sensor.set_pixformat(Sensor.RGB565)

    # 初始化显示屏
    Display.init(Display.ST7701, to_ide=True, width=800, height=480)
    MediaManager.init()
    sensor.run()

    clock = time.clock()

    # 裁剪图像的ROI，格式为(x, y, w, h)
    cut_roi = (540, 300, 480, 480)
#    cut_roi = (0,0,800,480)

    # 向屏幕输出图像
    def show_img_2_screen():
        global img
        if(img.height()>480 or img.width() > 800):
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)
#        Display.show_image(img)

    # 触摸屏初始化
    tp = TOUCH(0)

    # 存储阈值字典
    threshold_dict = {
        'rect': [],  # 矩形检测阈值（灰度）
        'red_point': []  # 激光点检测阈值（LAB颜色空间）
    }

    # 触摸计数器，达到一定的数值后开启阈值编辑模式，防止误触
    touch_counter = 0

    # 状态标识
    # flag = 0: 正常显示摄像头画面，等待检测矩形
    # flag = 1: 阈值调整模式
    flag = 0

    # 主循环
    while True:
        clock.tick()
        os.exitpoint()

        # flag = 0: 正常显示模式
        if flag == 0:
            img = sensor.snapshot()
            img = img.copy(roi=cut_roi)

            # 尝试检测矩形（如果有阈值的话）
            if threshold_dict['rect']:
                img_rect = img.to_grayscale(copy=True)
                img_rect = img_rect.binary(threshold_dict['rect'])
                rects = img_rect.find_rects(threshold=10000)

                if rects:
                    for rect in rects:
                        corner = rect.corners()
                        img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)

            show_img_2_screen()

        # flag = 1: 阈值调整模式
        elif flag == 1:

            # UI颜色定义
            button_color = (150, 150, 150)
            text_color = (0, 0, 0)
            lab_color = (255, 100, 0)  # LAB标识的颜色

            # 创建画布，用来绘制按钮和界面
            img = image.Image(800, 480, image.RGB565)
            img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

            # 标题栏
            img.draw_rectangle(160, 0, 480, 40, color=(100, 150, 200), thickness=2, fill=True)
            img.draw_string_advanced(300, 5, 30, "阈值调整工具", color=(255, 255, 255))

            # 按钮--返回
            img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(50, 10, 30, "返回", color=text_color)

            # 按钮--切换
            img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(800-110, 10, 30, "切换", color=text_color)

            # 按钮--归位
            img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(50, 450, 30, "归位", color=text_color)

            # 按钮--保存
            img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(800-110, 450, 30, "保存", color=text_color)

            # 绘制滑块控制按钮（左侧减少，右侧增加）
            for j in [0, 800 - 160]:
                for i in range(60, 420, 60):
                    img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

            # LAB颜色空间标识 - 改善的UI部分
            lab_labels = ["L-", "A-", "B-"]
            lab_labels_plus = ["L+", "A+", "B+"]
            gray_labels = ["灰度-", "灰度+"]

            # 添加LAB颜色空间说明
            img.draw_rectangle(160, 45, 480, 25, color=(255, 200, 100), thickness=1, fill=True)
            img.draw_string_advanced(200, 50, 20, "LAB颜色空间: L(亮度) A(绿红) B(蓝黄)", color=(0, 0, 0))

            # 在左侧按钮上添加标签（减少值）
            for i in range(6):
                y_pos = 60 + i * 60
                if i < 3:
                    # L, A, B 的减少按钮
                    img.draw_string_advanced(60, y_pos + 5, 30, lab_labels[i], color=lab_color)
                    # 添加详细说明
                    if i == 0:
                        img.draw_string_advanced(10, y_pos + 25, 15, "亮度减", color=(100, 100, 100))
                    elif i == 1:
                        img.draw_string_advanced(10, y_pos + 25, 15, "绿色减", color=(100, 100, 100))
                    elif i == 2:
                        img.draw_string_advanced(10, y_pos + 25, 15, "蓝色减", color=(100, 100, 100))
                elif i == 3:
                    # 灰度减少按钮
                    img.draw_string_advanced(30, y_pos + 10, 25, gray_labels[0], color=text_color)

            # 在右侧按钮上添加标签（增加值）
            for i in range(6):
                y_pos = 60 + i * 60
                if i < 3:
                    # L, A, B 的增加按钮
                    img.draw_string_advanced(800-100, y_pos + 5, 30, lab_labels_plus[i], color=lab_color)
                    # 添加详细说明
                    if i == 0:
                        img.draw_string_advanced(800-150, y_pos + 25, 15, "亮度增", color=(100, 100, 100))
                    elif i == 1:
                        img.draw_string_advanced(800-150, y_pos + 25, 15, "红色增", color=(100, 100, 100))
                    elif i == 2:
                        img.draw_string_advanced(800-150, y_pos + 25, 15, "黄色增", color=(100, 100, 100))
                elif i == 3:
                    # 灰度增加按钮
                    img.draw_string_advanced(800-120, y_pos + 10, 25, gray_labels[1], color=text_color)

            # 判断按下的按钮是哪一个
            def witch_key(x, y):
                if x < 160:
                    if y < 40:
                        return "return"
                    if y > 480 - 40:
                        return "reset"
                    if not y > 60:
                        return None
                    if (y - 60) % 60 < 40:
                        return str((y - 60) // 60)
                elif x > 800-160:
                    if y < 40:
                        return "change"
                    if y > 480 - 40:
                        return "save"
                    if not y > 60:
                        return None
                    if (y - 60) % 60 < 40:
                        return str((y - 60) // 60 + 6)
                return None

            # 阈值调整模式列表
            threshold_mode_lst = list(threshold_dict.keys())
            threshold_mode = 'rect'  # 默认从矩形检测开始
            threshold_current = [0, 255, 0, 255, 0, 255]  # L, A, B 的最小值和最大值

            # 阈值调整主循环
            while True:
                # 重新创建画布，确保UI更新
                img = image.Image(800, 480, image.RGB565)
                img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

                # 重新绘制所有UI元素
                # 标题栏
                img.draw_rectangle(160, 0, 480, 40, color=(100, 150, 200), thickness=2, fill=True)
                img.draw_string_advanced(300, 5, 30, "阈值调整工具", color=(255, 255, 255))

                # 按钮--返回
                img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(50, 10, 30, "返回", color=text_color)

                # 按钮--切换
                img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(800-110, 10, 30, "切换", color=text_color)

                # 按钮--归位
                img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(50, 450, 30, "归位", color=text_color)

                # 按钮--保存
                img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(800-110, 450, 30, "保存", color=text_color)

                # 绘制滑块控制按钮
                for j in [0, 800 - 160]:
                    for i in range(60, 420, 60):
                        img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

                # 添加LAB标签
                lab_labels = ["L-", "A-", "B-"]
                lab_labels_plus = ["L+", "A+", "B+"]
                gray_labels = ["灰度-", "灰度+"]

                # 在左侧按钮上添加标签
                for i in range(6):
                    y_pos = 60 + i * 60
                    if i < 3:
                        img.draw_string_advanced(60, y_pos + 5, 30, lab_labels[i], color=lab_color)
                    elif i == 3:
                        img.draw_string_advanced(30, y_pos + 10, 25, gray_labels[0], color=text_color)

                # 在右侧按钮上添加标签
                for i in range(6):
                    y_pos = 60 + i * 60
                    if i < 3:
                        img.draw_string_advanced(800-100, y_pos + 5, 30, lab_labels_plus[i], color=lab_color)
                    elif i == 3:
                        img.draw_string_advanced(800-120, y_pos + 10, 25, gray_labels[1], color=text_color)

                # 获取图像
                img_ = sensor.snapshot()
                img_ = img_.copy(roi=cut_roi)

                # 显示当前调整的模式
                mode_text = "矩形检测(灰度)" if threshold_mode == 'rect' else "激光点检测(LAB)"
                img.draw_rectangle(200, 180, 400, 35, color=(200, 200, 200), thickness=2, fill=True)
                img.draw_string_advanced(250, 185, 25, f"当前模式: {mode_text}", color=text_color)

                # 显示当前阈值 - 实时更新
                img.draw_rectangle(200, 220, 400, 80, color=(220, 220, 220), thickness=1, fill=True)
                if threshold_mode == 'rect':
                    img.draw_string_advanced(210, 225, 20, f"灰度阈值: [{threshold_current[0]}, {threshold_current[1]}]", color=text_color)
                    # 显示阈值范围程度
                    range_width = threshold_current[1] - threshold_current[0]
                    img.draw_string_advanced(210, 250, 18, f"阈值范围: {range_width}", color=(0, 100, 200))
                    img.draw_string_advanced(210, 270, 16, f"最小值: {threshold_current[0]}  最大值: {threshold_current[1]}", color=(100, 100, 100))
                else:
                    # LAB模式下分行显示
                    img.draw_string_advanced(210, 225, 18, f"L: [{threshold_current[0]}, {threshold_current[1]}]", color=lab_color)
                    img.draw_string_advanced(210, 240, 18, f"A: [{threshold_current[2]}, {threshold_current[3]}]", color=lab_color)
                    img.draw_string_advanced(210, 255, 18, f"B: [{threshold_current[4]}, {threshold_current[5]}]", color=lab_color)
                    # 显示各通道范围
                    l_range = threshold_current[1] - threshold_current[0]
                    a_range = threshold_current[3] - threshold_current[2]
                    b_range = threshold_current[5] - threshold_current[4]
                    img.draw_string_advanced(210, 275, 16, f"范围 L:{l_range} A:{a_range} B:{b_range}", color=(0, 100, 200))

                # 根据模式处理图像
                if threshold_mode == 'rect':
                    img_ = img_.to_grayscale()
                    img_ = img_.binary([threshold_current[:2]])
                    img_ = img_.to_rgb565()
                    # 添加矩形检测
                    rects = img_.find_rects(threshold=1000)
                    if rects:
                        for rect in rects:
                            corner = rect.corners()
                            # 绘制检测到的矩形
                            img_.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=3)
                            img_.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=3)
                            img_.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=3)
                            img_.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=3)

                elif threshold_mode == 'red_point':
                    # LAB颜色空间处理
                    img_lab = img_.copy()
                    img_ = img_.binary([[i - 127 for i in threshold_current]])
                    img_ = img_.to_rgb565()
                    # 添加激光点检测
                    blobs = img_lab.find_blobs([[i - 127 for i in threshold_current]], False,
                                             x_stride=1, y_stride=1,
                                             pixels_threshold=20, margin=False)
                    if blobs:
                        for blob in blobs:
                            img_.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                              color=(0, 255, 0), thickness=2, fill=False)
                            img_.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=10, thickness=2)

                # 在画布上显示处理后的图像
                img.draw_image(img_, (800-img_.width()) // 2, (480-img_.height()) // 2 + 80)

                # 处理触摸输入
                points = tp.read()
                if len(points) > 0:
                    button_ = witch_key(points[0].x, points[0].y)
                    if button_:
                        if button_ == "return":
                            # 返回正常显示模式
                            flag = 0
                            touch_counter = 0
                            time.sleep_ms(500)
                            break
                        elif button_ == "change":
                            # 切换调整模式
                            threshold_mode = threshold_mode_lst[(threshold_mode_lst.index(threshold_mode) + 1) % len(threshold_mode_lst)]
                            time.sleep_ms(500)
                        elif button_ == "reset":
                            # 归位滑块
                            threshold_current = [0, 255, 0, 255, 0, 255]
                            time.sleep_ms(500)
                        elif button_ == "save":
                            # 保存当前阈值
                            if threshold_mode == 'red_point':
                                threshold_dict[threshold_mode].append([i - 127 for i in threshold_current])
                            elif threshold_mode == 'rect':
                                threshold_dict[threshold_mode].append(threshold_current[:2])
                            img.draw_string_advanced(200, 200, 30, "保存成功", color=text_color)
                            time.sleep_ms(500)

                        else:
                            # 调整阈值滑块
                            button_num = int(button_)
                            old_value = threshold_current.copy()

                            if button_num >= 6:
                                # 右侧按钮，增加阈值
                                index = button_num - 6
                                if threshold_mode == 'rect' and index < 2:
                                    threshold_current[index] = min(255, threshold_current[index] + 5)
                                elif threshold_mode == 'red_point' and index < 6:
                                    threshold_current[index] = min(255, threshold_current[index] + 5)
                            elif button_num < 6:
                                # 左侧按钮，减少阈值
                                if threshold_mode == 'rect' and button_num < 2:
                                    threshold_current[button_num] = max(0, threshold_current[button_num] - 5)
                                elif threshold_mode == 'red_point' and button_num < 6:
                                    threshold_current[button_num] = max(0, threshold_current[button_num] - 5)

                            time.sleep_ms(80)  # 防止按键过快

                show_img_2_screen()

        # 实现长按屏幕进入阈值编辑模式的效果
        points = tp.read()
        if len(points) > 0:
            touch_counter += 1
            if touch_counter > 20:  # 长按约1秒进入阈值调整模式
                flag = 1
                touch_counter = 0
        else:
            touch_counter -= 2
            touch_counter = max(0, touch_counter)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
